/* CSS Document */

/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
}

body {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	line-height: 1.6;
	color: #333;
	background: url('../img/page_bg.jpg') repeat;
}

/* Container */
#container {
	width: 800px;
	margin: 0 auto;
	background-color: #fff;
	border: 1px solid #ccc;
}

/* Header */
#header {
	height: 150px;
	background: url('../img/header.jpg') no-repeat center;
	background-size: cover;
	position: relative;
	text-align: center;
	color: #fff;
	padding-top: 30px;
}

#header h1 {
	font-size: 36px;
	font-weight: bold;
	margin-bottom: 10px;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

#header h2 {
	font-size: 18px;
	font-weight: normal;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Content */
#content {
	padding: 20px 30px;
	background-color: #fff;
	min-height: 400px;
}

#main h3 {
	font-size: 16px;
	font-weight: bold;
	color: #2c5aa0;
	margin: 20px 0 10px 0;
	border-bottom: 1px solid #ddd;
	padding-bottom: 5px;
}

#main h3:first-child {
	margin-top: 0;
}

#main p {
	margin-bottom: 15px;
	text-align: justify;
}

#main ul {
	margin: 10px 0 15px 20px;
}

#main li {
	margin-bottom: 5px;
}

#main a {
	color: #2c5aa0;
	text-decoration: none;
}

#main a:hover {
	text-decoration: underline;
}

/* Footer */
#footer {
	height: 80px;
	background: url('../img/footer.jpg') no-repeat center;
	background-size: cover;
	text-align: center;
	color: #fff;
	padding-top: 25px;
}

#footer p {
	font-size: 11px;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}
