/* CSS Document */

/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
}

body {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
	line-height: 1.4;
	color: #333;
	background: url('../img/page_bg.jpg') repeat;
}

/* Container */
#container {
	width: 600px;
	margin: 20px auto;
	background-color: #f4f1e8;
	border: 3px solid #8b7355;
}

/* Header */
#header {
	height: 120px;
	background: url('../img/header.jpg') no-repeat;
	background-size: cover;
	position: relative;
	padding: 20px 0 0 30px;
}

#header h1 {
	font-size: 24px;
	font-weight: bold;
	color: #f4f1e8;
	letter-spacing: 3px;
	margin-bottom: 5px;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

#header h2 {
	font-size: 11px;
	font-weight: normal;
	color: #f4f1e8;
	letter-spacing: 1px;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

/* Content */
#content {
	display: flex;
	background-color: #f4f1e8;
	min-height: 400px;
}

#main {
	width: 450px;
	padding: 15px 0 15px 15px;
}

#main h3 {
	font-size: 11px;
	font-weight: bold;
	color: #8b7355;
	margin: 15px 0 8px 0;
	text-transform: uppercase;
	letter-spacing: 1px;
}

#main h3:first-child {
	margin-top: 0;
}

#main p {
	margin-bottom: 12px;
	text-align: justify;
	color: #333;
}

/* Sidebar */
#sidebar {
	width: 150px;
	background-color: #6b5d47;
	padding: 15px 0;
	color: #f4f1e8;
}

#sidebar h4 {
	font-size: 11px;
	font-weight: bold;
	color: #f4f1e8;
	margin: 0 15px 10px 15px;
	text-transform: uppercase;
	letter-spacing: 1px;
}

#sidebar p {
	font-size: 10px;
	margin: 8px 15px 5px 15px;
	color: #f4f1e8;
}

#sidebar ul {
	margin: 0 15px 10px 15px;
	list-style: none;
}

#sidebar li {
	margin-bottom: 3px;
	font-size: 10px;
}

#sidebar a {
	color: #4a90e2;
	text-decoration: none;
	font-size: 10px;
}

#sidebar a:hover {
	text-decoration: underline;
}

/* Footer */
#footer {
	height: 60px;
	background: url('../img/footer.jpg') no-repeat;
	background-size: cover;
	text-align: center;
	color: #f4f1e8;
	padding-top: 20px;
}

#footer p {
	font-size: 10px;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}
